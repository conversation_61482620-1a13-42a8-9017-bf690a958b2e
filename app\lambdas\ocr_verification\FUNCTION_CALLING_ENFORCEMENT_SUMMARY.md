# Gemini Function Calling Enforcement Summary

## Overview

This document summarizes the implementation of **enforced function calling** with **dynamic schema generation** based on request parameters for the Gemini LLM integration.

## Key Changes Implemented

### 1. **Enforced Function Calling**

#### **Before:**
- Function calling was optional (controlled by `GEMINI_STRUCTURED_OUTPUT`)
- Had fallback to text parsing when function calling failed
- Used static function schemas

#### **After:**
- Function calling is **ENFORCED** when `business_rules` are provided
- **No fallback** to text parsing when function calling is enforced
- Dynamic function schemas generated based on request parameters
- Returns error if function calling fails when enforced

### 2. **Dynamic Schema Generation**

#### **New Method: `_create_dynamic_function_schema()`**
```python
@classmethod
def _create_dynamic_function_schema(cls, business_rules, has_image=False):
    # Creates dynamic schema based on business_rules
    # Different schemas for image vs text extraction
```

#### **Dynamic Properties:**
- **business_rules**: Generated dynamically from request parameters
- **Field-specific descriptions**: Each business rule gets its own description
- **Required fields**: All business rules are marked as required
- **Image vs Text**: Different schemas for image-based vs text-based extraction

### 3. **Runtime Parameter Injection**

#### **Method Signature Updated:**
```python
def converse_model(cls, messages=[], business_rules=None):
```

#### **Process Flow:**
1. **process_text_pdf()** extracts `business_rules` from payload
2. **process_scanned_pdf()** extracts `business_rules` from payload  
3. **invoke_model()** passes `business_rules` to LLM
4. **GeminiLlm.converse_model()** uses `business_rules` for dynamic schema

#### **Function Calling Chain:**
```
process_text_pdf() → invoke_model() → GeminiLlm.converse_model()
                         ↓
             business_rules passed through entire chain
```

### 4. **Error Handling for Enforcement**

#### **Setup Failures:**
- Function schema generation failure → Return error immediately
- No longer proceed without function calling when enforced

#### **Response Failures:**
- No function call in response → Return error with clear message
- Function call extraction failure → Return error with details

#### **Logging Enhancement:**
- **Function calling setup**: Shows whether enforcement is active
- **Dynamic schema**: Logs the generated schema at runtime
- **Enforcement status**: Clear logging when function calling is enforced vs optional

## Implementation Details

### **Dynamic Schema Structure**

#### **For Image-based Extraction:**
```json
{
  "name": "extract_document_data",
  "parameters": {
    "type": "object",
    "properties": {
      "business_rules": {
        "type": "object",
        "properties": {
          "property_price": {"type": "string", "description": "Extracted value for property price field"},
          "property_size": {"type": "string", "description": "Extracted value for property size field"},
          // ... other business rules dynamically added
        },
        "required": ["property_price", "property_size", ...]
      },
      "llm_quality": {
        // Quality assessment fields for image extraction
      }
    }
  }
}
```

#### **For Text-based Extraction:**
```json
{
  "name": "extract_text_data",
  "parameters": {
    "type": "object",
    "properties": {
      "business_rules": {
        "type": "object",
        "properties": {
          // Same dynamic structure as image-based
        }
      }
      // No llm_quality for text extraction
    }
  }
}
```

### **Enforcement Logic**

```python
if business_rules:
    # ENFORCED MODE
    - Generate dynamic schema
    - Configure function calling tools
    - Require function call in response
    - Return error if function calling fails
else:
    # OPTIONAL MODE (backward compatibility)
    - Standard text-based processing
    - Optional function calling if available
```

## Testing Updates

### **Test Script Enhancements:**
- Updated `test_gemini_optimized.py` to pass `business_rules`
- Added logging to show enforcement status
- Tests both text and image extraction with enforcement

### **Verification Points:**
1. **Schema Generation**: Verify dynamic schema matches business_rules
2. **Function Calling**: Ensure function calls are made when enforced
3. **Error Handling**: Test failure scenarios when enforcement is active
4. **Response Structure**: Validate function call responses match schema

## Benefits

### **1. Reliability:**
- **Guaranteed structured output** when business_rules are provided
- **No silent fallbacks** that might produce inconsistent results
- **Clear error messages** when function calling fails

### **2. Flexibility:**
- **Dynamic schemas** adapt to different business rule sets
- **Runtime parameter injection** eliminates hardcoded schemas
- **Field-specific descriptions** improve extraction accuracy

### **3. Performance:**
- **Direct function calling** bypasses text parsing overhead
- **Reduced post-processing** needed for structured data
- **Clear success/failure states** for better error handling

### **4. Maintainability:**
- **Single source of truth** for business rules (the request payload)
- **No schema duplication** across different extraction types
- **Consistent enforcement** across text and image processing

## Usage

### **For Text Processing:**
```python
result = invoke_model(prompt, business_rules=business_rules)
```

### **For Image Processing:**
```python
result = extract_parameters_from_image(image, business_rules, existing_data)
```

### **Direct LLM Call:**
```python
response = GeminiLlm.converse_model(messages=messages, business_rules=business_rules)
```

## Backward Compatibility

- **Existing calls without business_rules** continue to work normally
- **No breaking changes** to existing API
- **Enforcement only applies** when business_rules are explicitly provided
- **Fallback behavior** remains for legacy usage patterns

---

*This implementation ensures reliable, structured data extraction while maintaining flexibility and backward compatibility.*
