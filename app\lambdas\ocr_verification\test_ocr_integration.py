#!/usr/bin/env python3
"""
Simple test script to verify OCR integration with external API
"""
import os
import sys
import time

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.services.lambda_ocr_service import (
    assess_document_quality_with_lambda_ocr,
    assess_pdf_quality_with_lambda_ocr,
)


def test_ocr_image_api():
    """Test OCR image API integration"""
    print("\n🧪 Testing OCR Image API Integration")
    print("-" * 50)
    
    # Create a simple test image (or use existing one)
    test_image_path = "test_document_quality.png"
    
    if not os.path.exists(test_image_path):
        print(f"❌ Test image not found: {test_image_path}")
        print("   Please run test_lambda_ocr_quality.py first to create a test image")
        return False
    
    try:
        # Load image
        with open(test_image_path, 'rb') as f:
            image_bytes = f.read()
        
        print(f"📄 Test image loaded: {len(image_bytes)} bytes")
        
        # Test OCR API
        start_time = time.time()
        result = assess_document_quality_with_lambda_ocr(image_bytes)
        processing_time = time.time() - start_time
        
        print(f"⏱️ Processing time: {processing_time:.2f} seconds")
        
        if result["status"] == "SUCCESS":
            quality_data = result["result"]
            rapid_confidence = quality_data["rapid_confidence"]
            quality_assessment = quality_data["quality_assessment"]
            
            print("✅ OCR API Integration Successful!")
            print(f"   Quality Level: {quality_assessment['quality_level']}")
            print(f"   Quality Score: {quality_assessment['quality_score']:.2f}")
            print(f"   Quality Acceptable: {quality_assessment['quality_acceptable']}")
            print(f"   Confidence Threshold: {quality_assessment['confidence_threshold']}%")
            
            print(f"\n📊 OCR Confidence Metrics:")
            print(f"   Geometric Mean: {rapid_confidence['geometric_mean']:.2f}%")
            print(f"   Total Words: {rapid_confidence['total_words']}")
            
            print(f"\n📝 Assessment Summary:")
            print(f"   {quality_assessment['assessment_summary']}")
            
            return True
        else:
            print(f"❌ OCR API integration failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OCR API integration: {e}")
        return False


def test_ocr_pdf_api():
    """Test OCR PDF API integration"""
    print("\n🧪 Testing OCR PDF API Integration")
    print("-" * 50)
    
    # Look for a test PDF file
    test_pdf_path = None
    for filename in ["sample_insurance_claim.pdf", "test_document.pdf", "Tenancy Contract (Signed) - BV 235 1.pdf"]:
        if os.path.exists(filename):
            test_pdf_path = filename
            break
    
    if not test_pdf_path:
        print("❌ No test PDF found")
        print("   Please place a test PDF file in the current directory")
        return False
    
    try:
        # Load PDF
        with open(test_pdf_path, 'rb') as f:
            pdf_bytes = f.read()
        
        print(f"📄 Test PDF loaded: {len(pdf_bytes)} bytes")
        
        # Test OCR API
        start_time = time.time()
        result = assess_pdf_quality_with_lambda_ocr(pdf_bytes)
        processing_time = time.time() - start_time
        
        print(f"⏱️ Processing time: {processing_time:.2f} seconds")
        
        if result["status"] == "SUCCESS":
            quality_data = result["result"]
            rapid_confidence = quality_data["rapid_confidence"]
            quality_assessment = quality_data["quality_assessment"]
            
            print("✅ OCR PDF API Integration Successful!")
            print(f"   Quality Level: {quality_assessment['quality_level']}")
            print(f"   Quality Score: {quality_assessment['quality_score']:.2f}")
            print(f"   Quality Acceptable: {quality_assessment['quality_acceptable']}")
            print(f"   Confidence Threshold: {quality_assessment['confidence_threshold']}%")
            
            print(f"\n📊 OCR Confidence Metrics:")
            print(f"   Geometric Mean: {rapid_confidence['geometric_mean']:.2f}%")
            print(f"   Total Words: {rapid_confidence['total_words']}")
            
            print(f"\n📝 Assessment Summary:")
            print(f"   {quality_assessment['assessment_summary']}")
            
            return True
        else:
            print(f"❌ OCR PDF API integration failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OCR PDF API integration: {e}")
        return False


def main():
    """Main test function"""
    print("🔍 External OCR API Integration Testing")
    print("=" * 50)
    
    # Test image API
    image_success = test_ocr_image_api()
    
    # Test PDF API
    pdf_success = test_ocr_pdf_api()
    
    print("\n" + "=" * 50)
    if image_success and pdf_success:
        print("🎉 All OCR API integration tests passed!")
    else:
        print("⚠️ Some OCR API integration tests failed")
        if not image_success:
            print("   - Image API test failed")
        if not pdf_success:
            print("   - PDF API test failed")
    
    print("\n💡 Next steps:")
    print("   - Deploy the updated Lambda function")
    print("   - Test with real documents")
    print("   - Monitor OCR API performance and costs")


if __name__ == "__main__":
    main()
