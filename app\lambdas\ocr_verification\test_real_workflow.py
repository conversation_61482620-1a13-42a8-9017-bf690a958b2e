#!/usr/bin/env python3
"""
Real-world test to validate Gemini workflow with actual SSM configuration and API calls.
This test uses real AWS SSM parameters and Gemini API to validate the complete workflow.
"""

import os
import sys
import json
from PIL import Image
import io

# Set test environment to use environment variables instead of SSM
os.environ['ENV'] = 'test'
os.environ['LLM_PROVIDER'] = 'gemini'

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.llm.gemini import GeminiLlm
from core.llm.bedrock import BedrockLlm
from core.services.process import create_extraction_prompt, extract_parameters_from_image, verify_claims
from core.llm.llm_factory import LlmFactory
from config.config import config


def create_test_document_image():
    """Create a test document image with sample text."""
    from PIL import Image, ImageDraw, ImageFont
    
    # Create a white background image
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # Add sample document text
    text_lines = [
        "INSURANCE CLAIM DOCUMENT",
        "",
        "Policy Number: POL-2024-123456",
        "Claim Amount: $75,000",
        "Property Address: 456 Oak Street, Springfield, IL 62701",
        "Incident Date: 2024-01-15",
        "Claim Type: Property Damage",
        "Description: Water damage from burst pipe",
        "",
        "Claimant: John Smith",
        "Phone: (*************",
        "Email: <EMAIL>"
    ]
    
    y_position = 50
    for line in text_lines:
        draw.text((50, y_position), line, fill='black', font=font)
        y_position += 35
    
    return img


def test_real_gemini_extraction():
    """Test real Gemini API extraction with function calling."""
    print("🧪 Testing real Gemini API extraction...")
    
    # Set LLM provider to Gemini
    os.environ['LLM_PROVIDER'] = 'gemini'
    
    business_rules = ["policy_number", "claim_amount", "property_address", "incident_date"]
    
    # Test text extraction
    sample_text = """
    Policy Number: POL-2024-123456
    Claim Amount: $75,000
    Property Address: 456 Oak Street, Springfield, IL 62701
    Incident Date: 2024-01-15
    """
    
    try:
        # Create prompt for Gemini
        prompt = create_extraction_prompt(sample_text, business_rules)
        print(f"✅ Gemini prompt created successfully")
        
        # Test with actual Gemini API
        messages = [{"role": "user", "content": [{"text": prompt}]}]
        result = GeminiLlm.converse_model(messages=messages, business_rules=business_rules)
        
        if result["status"] == "SUCCESS":
            print(f"✅ Gemini API call successful")
            print(f"📊 Token usage: {result.get('llm_usage', {})}")
            
            # Parse the result
            result_str = result["result"]
            if result_str.startswith('<json>') and result_str.endswith('</json>'):
                result_str = result_str[6:-7]
            
            extracted_data = json.loads(result_str)
            print(f"📋 Extracted data: {json.dumps(extracted_data, indent=2)}")
            
            # Validate extraction
            assert "business_rules" in extracted_data
            assert "policy_number" in extracted_data["business_rules"]
            print("✅ Real Gemini extraction test passed!")
            return True
        else:
            print(f"❌ Gemini API call failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Real Gemini test failed: {str(e)}")
        return False


def test_real_image_extraction():
    """Test real image extraction with Gemini."""
    print("\n🧪 Testing real image extraction with Gemini...")
    
    business_rules = ["policy_number", "claim_amount", "property_address"]
    
    try:
        # Create test document image
        test_image = create_test_document_image()
        print("✅ Test document image created")
        
        # Test image extraction
        result = extract_parameters_from_image(test_image, business_rules)
        
        if result["status"] == "SUCCESS":
            print(f"✅ Image extraction successful")
            print(f"📊 Token usage: {result.get('llm_usage', {})}")
            
            # Parse and validate result
            extracted_data = result.get("extracted_data", {})
            print(f"📋 Extracted data: {json.dumps(extracted_data, indent=2)}")
            
            print("✅ Real image extraction test passed!")
            return True
        else:
            print(f"❌ Image extraction failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Real image extraction test failed: {str(e)}")
        return False


def test_real_claims_verification():
    """Test real claims verification workflow."""
    print("\n🧪 Testing real claims verification...")
    
    user_claims = {
        "policy_number": "POL-2024-123456",
        "claim_amount": "75000",
        "property_address": "456 Oak Street, Springfield, IL 62701",
        "incident_date": "2024-01-15"
    }
    
    verified_claims = {
        "policy_number": "POL-2024-123456",
        "claim_amount": "74500",  # Within 10% buffer
        "property_address": "456 Oak St, Springfield, IL 62701",  # Slight variation
        "incident_date": "2024-01-15"
    }
    
    try:
        result = verify_claims(user_claims, verified_claims)
        
        if result["status"] == "SUCCESS":
            print(f"✅ Claims verification successful")
            print(f"📊 Token usage: {result.get('llm_usage', {})}")
            
            # Parse verification result
            verification_data = result.get("result", "{}")
            if verification_data.startswith('<json>') and verification_data.endswith('</json>'):
                verification_data = verification_data[6:-7]
            
            if verification_data.strip():
                parsed_result = json.loads(verification_data)
                print(f"📋 Verification result: {json.dumps(parsed_result, indent=2)}")
            
            print("✅ Real claims verification test passed!")
            return True
        else:
            print(f"❌ Claims verification failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Real claims verification test failed: {str(e)}")
        return False


def test_configuration_loading():
    """Test that configuration loads correctly."""
    print("\n🧪 Testing configuration loading...")

    try:
        print(f"📋 Environment: {os.environ.get('ENV', 'not set')}")
        print(f"📋 LLM Provider: {config.ssm_config.get('llm_provider', 'not set')}")

        # Check Gemini configuration
        gemini_config = config.ssm_config.get('gemini_inference_config')
        if gemini_config:
            if isinstance(gemini_config, str):
                gemini_data = json.loads(gemini_config)
            else:
                gemini_data = gemini_config
            print(f"📋 Gemini model: {gemini_data.get('gemini_model_name', 'not set')}")
            print(f"📋 Gemini API key configured: {'Yes' if gemini_data.get('gemini_api_key') else 'No'}")

        # Check prompts
        print(f"📋 Prompts loaded: {len(config.prompts)} prompts")

        # Check if Gemini client is initialized
        from core.llm.gemini import gemini_client
        print(f"📋 Gemini client initialized: {'Yes' if gemini_client else 'No'}")

        print("✅ Configuration loading test passed!")
        return True

    except Exception as e:
        print(f"❌ Configuration loading test failed: {str(e)}")
        return False


def main():
    """Run all real-world tests."""
    print("🚀 Starting real-world workflow tests with environment configuration...\n")
    
    test_results = []
    
    try:
        # Test configuration loading
        test_results.append(test_configuration_loading())
        
        # Test real Gemini extraction
        test_results.append(test_real_gemini_extraction())
        
        # Test real image extraction
        test_results.append(test_real_image_extraction())
        
        # Test real claims verification
        test_results.append(test_real_claims_verification())
        
        # Summary
        passed_tests = sum(test_results)
        total_tests = len(test_results)
        
        print(f"\n📊 Test Results: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("\n🎉 All real-world tests passed!")
            print("\n✅ Implementation Summary:")
            print("✅ SSM configuration loading works")
            print("✅ Gemini function calling with real API works")
            print("✅ Image extraction with real API works")
            print("✅ Claims verification workflow operational")
            print("✅ Complete Bedrock → Gemini migration successful")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} test(s) failed")
            print("Check the error messages above for details")
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
