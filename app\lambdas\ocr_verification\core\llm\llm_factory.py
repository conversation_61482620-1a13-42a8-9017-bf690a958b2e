"""
LLM Factory module for managing different LLM providers.
Provides a unified interface for switching between Bedrock, Gemini, and future LLM providers.
"""

import os
from config.config import config
from core.logger.logger import get_logger, log_frame_info, log_frame_error
from core.utils.utils import get_json_value

# Load configuration object containing SSM parameters
config_obj = config.ssm_config

# Initialize logger for the current module
logger = get_logger(__name__)


class LlmFactory:
    """
    Factory class for creating and managing LLM provider instances.
    Supports multiple providers and allows runtime switching based on configuration.
    """
    
    # Supported LLM providers
    BEDROCK = "bedrock"
    GEMINI = "gemini"
    
    # Cache for provider instances
    _provider_instances = {}
    
    @classmethod
    def get_llm_provider(cls):
        """
        Get the configured LLM provider instance.
        
        Returns:
            LLM provider instance with converse_model method
        """
        # Log the source values for debugging
        ssm_provider = config_obj.get("llm_provider")
        env_provider = os.getenv("LLM_PROVIDER")
        
        log_frame_info(logger, "🔍 LLM Provider Selection Debug:")
        log_frame_info(logger, f"  📋 SSM config object keys: {list(config_obj.keys()) if config_obj else 'None'}")
        log_frame_info(logger, f"  🔧 SSM llm_provider value: '{ssm_provider}'")
        log_frame_info(logger, f"  🌍 Environment LLM_PROVIDER: '{env_provider}'")
        log_frame_info(logger, f"  🎯 Default fallback: '{cls.GEMINI}'")
        
        # Get provider from config or environment, default to gemini
        provider_name = (
            ssm_provider or 
            env_provider or
            cls.GEMINI
        ).lower().strip()
        
        log_frame_info(logger, f"🚀 Selected LLM provider: '{provider_name}'")
        
        # Log selection logic
        if ssm_provider:
            log_frame_info(logger, f"✅ Provider selected from SSM Parameter Store: '{ssm_provider}'")
        elif env_provider:
            log_frame_info(logger, f"⚠️ Provider selected from Environment Variable (SSM not available): '{env_provider}'")
        else:
            log_frame_info(logger, f"🔄 Provider using default fallback (no SSM or env): '{cls.GEMINI}'")
        
        # Return cached instance if available
        if provider_name in cls._provider_instances:
            log_frame_info(logger, f"♻️ Returning cached {provider_name} provider instance")
            return cls._provider_instances[provider_name]
        
        log_frame_info(logger, f"🆕 Creating new {provider_name} provider instance")
        
        # Create new provider instance
        provider_instance = cls._create_provider(provider_name)
        
        # Cache the instance
        cls._provider_instances[provider_name] = provider_instance
        log_frame_info(logger, f"💾 Cached {provider_name} provider instance")
        
        return provider_instance
    
    @classmethod
    def _create_provider(cls, provider_name):
        """
        Create a new LLM provider instance.
        
        Args:
            provider_name: Name of the provider to create
            
        Returns:
            LLM provider instance
            
        Raises:
            ValueError: If provider is not supported
            ImportError: If provider module cannot be imported
        """
        try:
            if provider_name == cls.BEDROCK:
                from core.llm.bedrock import BedrockLlm
                log_frame_info(logger, message="Initialized Bedrock LLM provider")
                return BedrockLlm
                
            elif provider_name == cls.GEMINI:
                from core.llm.gemini import GeminiLlm
                log_frame_info(logger, message="Initialized Gemini LLM provider")
                return GeminiLlm
                
            else:
                raise ValueError(f"Unsupported LLM provider: {provider_name}")
                
        except ImportError as e:
            log_frame_error(
                logger, 
                message=f"Failed to import LLM provider '{provider_name}'", 
                error=str(e)
            )
            # Fallback to Gemini if the requested provider fails
            if provider_name != cls.GEMINI:
                log_frame_info(logger, message="Falling back to Gemini LLM provider")
                from core.llm.gemini import GeminiLlm
                return GeminiLlm
            else:
                raise
    
    @classmethod
    def get_available_providers(cls):
        """
        Get list of available LLM providers.
        
        Returns:
            List of available provider names
        """
        return [cls.BEDROCK, cls.GEMINI]
    
    @classmethod
    def clear_cache(cls):
        """
        Clear the provider instance cache.
        Useful for testing or when switching providers dynamically.
        """
        cls._provider_instances.clear()
        log_frame_info(logger, message="LLM provider cache cleared")


# Convenience function for getting the current LLM provider
def get_llm():
    """
    Convenience function to get the current LLM provider.
    
    Returns:
        LLM provider instance with converse_model method
    """
    return LlmFactory.get_llm_provider() 