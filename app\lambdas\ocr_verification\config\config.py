import os
import boto3
import json
from botocore.exceptions import Client<PERSON><PERSON><PERSON>
from concurrent.futures import ThreadPoolExecutor, as_completed
from dotenv import load_dotenv

from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_error,
)

# Initialize logger for this module
logger = get_logger(__name__)

# Load environment variables from .env file
load_dotenv()


class Config:
    """
    Singleton configuration class that manages AWS SSM parameters and S3-based prompts
    for different environments (development, staging, production).
    """

    def get(self, key, default=None):
        """
        Get a configuration value by key with an optional default value.

        Args:
            key (str): The configuration key to retrieve
            default: The default value to return if key is not found

        Returns:
            The configuration value if found, otherwise the default value
        """
        return self.ssm_config.get(key, default)

    @property
    def prompts(self):
        """
        Access the prompts with dictionary-like interface.
        
        Returns:
            A dictionary-like object that supports get() method
        """
        if not hasattr(self, '_prompts_dict'):
            self._prompts_dict = {}
            
        class PromptsDict:
            def __init__(self, prompts):
                self._prompts = prompts
                
            def get(self, key, default=""):
                return self._prompts.get(key, default)
                
            def __getitem__(self, key):
                return self._prompts[key]
                
        return PromptsDict(self._prompts_dict)

    _instance = None

    def __new__(cls, ssm_params_file="ssm_config.json", prompts_file="prompts.json"):
        """
        Singleton pattern implementation - ensures only one instance of Config exists.
        """
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialized = False
            cls._instance.ssm_params_file = ssm_params_file
            cls._instance.prompts_file = prompts_file
        return cls._instance

    def __init__(self, ssm_params_file="ssm_config.json", prompts_file="prompts.json"):
        """
        Initialize the configuration manager.

        Args:
            ssm_params_file: JSON file containing SSM parameter mappings for each environment
            prompts_file: JSON file containing prompt file names for S3 retrieval
        """
        # Prevent re-initialization of singleton instance
        if self._initialized:
            return

        # Map environment strings to SSM parameter prefixes
        env_raw = os.getenv("ENV", "development").lower()
        env_mapping = {
            "development": "DEV_SSM",
            "dev": "DEV_SSM",
            "staging": "STG_SSM",
            "stg": "STG_SSM",
            "production": "PROD_SSM",
            "prod": "PROD_SSM",
        }
        self.env = env_mapping.get(env_raw, "DEV_SSM")

        # Set AWS region from environment variable or use default
        self.aws_region = os.getenv("DEFAULT_REGION", "ap-southeast-1")

        self._ssm_client = None
        self._s3_client = None

        # Convert relative file paths to absolute paths
        self.ssm_params_file = self.get_absolute_path(ssm_params_file)
        self.prompts_file = self.get_absolute_path(prompts_file)

        # Load all SSM parameters by environment from JSON
        self.all_ssm_params = self._load_all_ssm_params()

        # Get param mapping for current environment
        if self.env not in self.all_ssm_params:
            available_keys = list(self.all_ssm_params.keys())
            raise ValueError(
                f"ENV config key '{self.env}' not found in ssm_config.json. "
                f"Raw ENV value was '{env_raw}'. "
                f"Available keys: {available_keys}. "
                f"Check that ENV environment variable is set correctly."
            )

        # Extract SSM parameter mapping for current environment
        self.ssm_param_map = self.all_ssm_params[self.env]

        # List of SSM parameter names for current environment
        self.ssm_param_names = list(self.ssm_param_map.values())

        # Load prompt file names from JSON
        self.prompt_files = self._load_prompt_files()

        # Load the config values and prompts
        self._load_config()
        self._initialized = True

    @property
    def ssm_client(self):
        """
        Lazy loading property for AWS SSM client.
        Creates the client only when first accessed.
        """
        if self._ssm_client is None:
            self._ssm_client = boto3.client("ssm", region_name=self.aws_region)
        return self._ssm_client

    @property
    def s3_client(self):
        """
        Lazy loading property for AWS S3 client.
        Creates the client only when first accessed.
        """
        if self._s3_client is None:
            self._s3_client = boto3.client("s3", region_name=self.aws_region)
        return self._s3_client

    def get_absolute_path(self, relative_path):
        """
        Convert relative file path to absolute path based on current module location.

        Args:
            relative_path: Path relative to this module's directory

        Returns:
            Absolute file path
        """
        current_dir = os.path.dirname(os.path.abspath(__file__))
        return os.path.join(current_dir, relative_path)

    def _load_all_ssm_params(self):
        """
        Load SSM parameter mappings from JSON configuration file.

        Returns:
            Dictionary containing SSM parameter mappings for all environments

        Raises:
            Exception: If file cannot be read or parsed
        """
        try:
            with open(self.ssm_params_file, "r") as f:
                return json.load(f)
        except Exception as e:
            log_frame_error(logger, f"Error loading SSM params: {e}")
            raise

    def _load_prompt_files(self):
        """
        Load prompt file configuration from JSON file.

        Returns:
            Dictionary mapping prompt keys to their S3 file names

        Raises:
            Exception: If file cannot be read or parsed
        """
        try:
            with open(self.prompts_file, "r") as f:
                return json.load(f)
        except Exception as e:
            log_frame_error(logger, f"Error loading prompts: {e}")
            raise

    def _load_config(self):
        """
        Main configuration loading method that:
        1. Fetches SSM parameters from AWS
        2. Creates configuration dictionary
        3. Loads prompts from S3

        Raises:
            ValueError: If required SSM parameters are missing
            Exception: If configuration loading fails
        """
        try:
            # Fetch all required SSM parameters
            self.parameters = self.get_ssm_parameters(self.ssm_param_names)

            # Check for missing parameters
            missing = [p for p in self.ssm_param_names if p not in self.parameters]
            if missing:
                raise ValueError(f"Missing SSM parameters: {missing}")

            # Create configuration dictionary mapping friendly names to parameter values
            self.ssm_config = {
                key: self.parameters[param] for key, param in self.ssm_param_map.items()
            }
            
            # Log the final SSM config mapping for debugging
            log_frame_info(logger, "🔧 Final SSM config mapping:")
            for key, value in sorted(self.ssm_config.items()):
                if any(sensitive in key.lower() for sensitive in ['api/key', 'api_key', 'password', 'secret']):
                    log_frame_info(logger, f"  {key}: ***MASKED***")
                else:
                    log_frame_info(logger, f"  {key}: {value}")

            # Load prompts from local files directly (bypassing S3)
            log_frame_info(logger, "🔄 Loading prompts from local files...")
            self.prompts = self._load_prompts_locally()
        except Exception as e:
            log_frame_error(
                logger, "Failed to load config", error=str(e), exc_info=True
            )
            raise

    def get_ssm_parameters(self, names):
        """
        Fetch SSM parameters from AWS in batches of 10 (AWS API limit).

        Args:
            names: List of SSM parameter names to fetch

        Returns:
            Dictionary mapping parameter names to their decrypted values

        Raises:
            ValueError: If any parameters are invalid
            ClientError: If AWS API call fails
            Exception: For other errors
        """
        try:
            log_frame_info(logger, f"Fetching SSM parameters for {self.env}")
            log_frame_info(logger, f"Parameter names to fetch: {names}")
            parameters = {}

            # Process parameters in batches of 10 (AWS SSM API limit)
            for i in range(0, len(names), 10):
                batch = names[i : i + 10]
                log_frame_info(logger, f"Fetching batch {i//10 + 1}: {batch}")
                
                response = self.ssm_client.get_parameters(
                    Names=batch, WithDecryption=True
                )

                # Add fetched parameters to result dictionary
                fetched_params = {p["Name"]: p["Value"] for p in response["Parameters"]}
                parameters.update(fetched_params)
                
                # Log fetched parameters with sensitive data masked
                for param_name, param_value in fetched_params.items():
                    if any(sensitive in param_name.lower() for sensitive in ['api/key', 'api_key', 'password', 'secret']):
                        masked_value = f"{param_value[:10]}..." if len(param_value) > 10 else "***MASKED***"
                        log_frame_info(logger, f"✅ Parameter fetched: {param_name} = {masked_value}")
                    else:
                        log_frame_info(logger, f"✅ Parameter fetched: {param_name} = {param_value}")

                # Check for invalid parameters
                if response.get("InvalidParameters"):
                    log_frame_error(logger, f"❌ Invalid parameters in batch: {response['InvalidParameters']}")
                    raise ValueError(
                        f"Invalid SSM parameters: {response['InvalidParameters']}"
                    )

            log_frame_info(logger, f"Successfully fetched {len(parameters)} SSM parameters")
            
            # Log summary of all fetched parameters (with masking)
            log_frame_info(logger, "📋 Summary of fetched parameters:")
            for param_name in sorted(parameters.keys()):
                if any(sensitive in param_name.lower() for sensitive in ['api/key', 'api_key', 'password', 'secret']):
                    log_frame_info(logger, f"  {param_name}: ***MASKED***")
                else:
                    log_frame_info(logger, f"  {param_name}: {parameters[param_name]}")
            
            return parameters
        except ClientError as e:
            log_frame_error(logger, "ClientError in SSM", error=str(e), exc_info=True)
            raise
        except Exception as e:
            log_frame_error(logger, f"General error fetching SSM: {e}", exc_info=True)
            raise

    def get_prompts_from_s3(self, bucket, prefix):
        """
        Fetch prompt files from S3 using concurrent threads for better performance.
        Falls back to local files if S3 is unavailable (useful for development).

        Args:
            bucket: S3 bucket name containing prompt files
            prefix: S3 key prefix for prompt files

        Returns:
            Dictionary mapping prompt keys to their content

        Raises:
            ValueError: If both S3 and local fallback fail
            Exception: For other S3 or threading errors
        """
        try:
            prompts = {}

            def fetch(key, fname):
                """
                Inner function to fetch a single prompt file from S3.

                Args:
                    key: Prompt identifier key
                    fname: S3 filename for the prompt

                Returns:
                    Tuple of (key, content)

                Raises:
                    ValueError: If S3 object cannot be fetched
                """
                s3_key = f"{prefix}{fname}"
                log_frame_info(logger, f"Fetching S3 key: {s3_key}")
                try:
                    obj = self.s3_client.get_object(Bucket=bucket, Key=s3_key)
                    return key, obj["Body"].read().decode("utf-8")
                except ClientError as e:
                    raise ValueError(f"Failed to fetch {key}: {e}")

            # Use ThreadPoolExecutor to fetch multiple prompts concurrently
            with ThreadPoolExecutor(max_workers=5) as executor:
                # Submit all fetch tasks
                futures = {
                    executor.submit(fetch, k, v): k
                    for k, v in self.prompt_files.items()
                }

                # Collect results as they complete
                for f in as_completed(futures):
                    k, content = f.result()
                    prompts[k] = content

            return prompts
        except Exception as e:
            log_frame_error(
                logger, "Failed to fetch prompts from S3", error=str(e), exc_info=True
            )
            
            # Fallback to local files if S3 fails
            log_frame_info(logger, "🔄 Attempting to load prompts from local files as fallback...")
            return self._load_prompts_locally()
    def _load_prompts_locally(self):
        """
        Load prompt files from local filesystem as a fallback when S3 is unavailable.
        
        Returns:
            Dictionary mapping prompt keys to their content
            
        Raises:
            ValueError: If local files cannot be loaded
        """
        try:
            self._prompts_dict = {}
            
            # Determine the prompts directory path
            # Look for prompts directory relative to current file location
            current_dir = os.path.dirname(os.path.abspath(__file__))
            
            # Try multiple possible paths for prompts directory
            possible_paths = [
                # Prompts are now included in Lambda package (/var/task/config -> /var/task/prompts)
                os.path.join(current_dir, "..", "prompts"),
                # If running from local development (config dir -> lambda_source/prompts)
                os.path.join(current_dir, "prompts"),
                # If running from local development (config dir -> app/prompts)
                os.path.join(current_dir, "..", "..", "..", "prompts"),
            ]
            
            prompts_dir = None
            for path in possible_paths:
                abs_path = os.path.abspath(path)
                if os.path.exists(abs_path):
                    prompts_dir = abs_path
                    log_frame_info(logger, f"📁 Found prompts directory: {prompts_dir}")
                    break
            
            if not prompts_dir:
                raise ValueError(f"Prompts directory not found. Tried paths: {possible_paths}")
            
            # Load each prompt file
            for prompt_key, filename in self.prompt_files.items():
                file_path = os.path.join(prompts_dir, filename)
                
                if not os.path.exists(file_path):
                    raise ValueError(f"Local prompt file not found: {file_path}")
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    self._prompts_dict[prompt_key] = content
                    log_frame_info(logger, f"✅ Loaded local prompt: {prompt_key} from {filename}")
                except Exception as e:
                    raise ValueError(f"Failed to read local prompt file {filename}: {e}")
            
            log_frame_info(logger, f"🎉 Successfully loaded {len(self._prompts_dict)} prompts from local files")
            return self._prompts_dict
        
        except Exception as e:
            log_frame_error(
                logger, "Failed to load prompts locally", error=str(e), exc_info=True
            )
            raise ValueError(f"Both S3 and local fallback failed to load prompts: {e}")


# Create singleton instance of Config class
config = Config()
