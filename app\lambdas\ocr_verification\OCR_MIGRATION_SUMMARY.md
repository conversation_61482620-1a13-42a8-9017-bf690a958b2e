# OCR Service Migration Summary

## Overview
Successfully migrated from AWS Textract to an external OCR API service. The migration maintains backward compatibility while replacing the underlying OCR processing engine.

## Changes Made

### 1. New OCR Service Module
- **File**: `core/services/lambda_ocr_service.py`
- **Purpose**: Replaces `textract_quality.py` with intelligent API calls
- **Key Features**:
  - Direct HTTP calls to external OCR service
  - Automatic size detection (>6MB = parallel processing, ≤6MB = single file)
  - Parallel PDF-to-image conversion for large documents
  - Parallel OCR processing per page for optimal performance
  - Image resizing for large images (>6MB)
  - Maintains same interface as Textract functions
  - Backward compatibility aliases

### 2. Updated Process Module
- **File**: `core/services/process.py`
- **Changes**:
  - Updated imports to use new OCR service
  - Replaced Textract function calls with OCR API calls
  - Updated error messages and logging

### 3. Configuration Updates
- **File**: `infra/cdk/lib/parameters.ts`
- **New Parameters**:
  - `ocr_service_url`: External OCR service URL
  - `ocr_confidence_threshold`: Confidence threshold for quality assessment
  - `ocr_pdf_timeout`: Timeout for PDF processing
  - `ocr_image_timeout`: Timeout for image processing
  - `ocr_max_workers`: Maximum concurrent workers (deprecated)

### 4. Test Files
- **Updated**: `test_lambda_ocr_quality.py` (renamed from `test_textract_quality.py`)
- **New**: `test_ocr_integration.py` - Simple integration test
- **New**: `test_parallel_ocr.py` - Parallel processing test for large PDFs
- **Backup**: `backup_textract/` - Contains original Textract files

## API Integration

### Image Processing
```python
# Call OCR service /ocr/image endpoint
response = requests.post(image_url, json={"image_base64": base64_image})
```

### PDF Processing
```python
# Smart PDF processing with size detection
if pdf_size > 6MB:
    # Convert to images and process in parallel
    images = convert_from_bytes(pdf_bytes, dpi=200)
    # Process each page in parallel with ThreadPoolExecutor
    results = parallel_process_images(images)
else:
    # Single file processing for small PDFs
    response = requests.post(pdf_url, files={"file": ("document.pdf", pdf_bytes, "application/pdf")})
```

### Response Format
The external OCR service returns the same format as expected:
```json
{
  "success": true,
  "detections": [
    {
      "text": "sample text",
      "confidence": 95.5,
      "bbox": [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
    }
  ],
  "page_statistics": [...],
  "total_detections": 156,
  "processing_time": 2.5
}
```

## Backward Compatibility

### Function Aliases
```python
# Old Textract functions still work
assess_document_quality_with_textract = assess_document_quality_with_lambda_ocr
assess_pdf_quality_with_textract = assess_pdf_quality_with_lambda_ocr
```

### Response Structure
- Maintains same `textract_confidence` field structure
- Same quality assessment logic
- Same confidence metrics calculation

## Benefits

1. **Intelligent Processing**: Automatic size detection and optimal processing method
2. **Parallel Performance**: Large PDFs processed in parallel for faster results
3. **Size Optimization**: Handles Lambda Function URL 6MB limit gracefully
4. **External Service**: Leverages specialized OCR service on another account
5. **Cost Optimization**: No AWS Textract charges
6. **Maintainability**: Clean, modular codebase with comprehensive error handling

## Configuration

### Required SSM Parameters
```typescript
// OCR service configuration
ocr_service_url: "https://42vwqwgpsjdwehfuc6rpmzlvj40loxhw.lambda-url.us-east-1.on.aws/"
ocr_confidence_threshold: "60"
ocr_pdf_timeout: "300"
ocr_image_timeout: "120"
```

### Default Values
- Service URL: `https://42vwqwgpsjdwehfuc6rpmzlvj40loxhw.lambda-url.us-east-1.on.aws/`
- PDF Timeout: 300 seconds (5 minutes)
- Image Timeout: 120 seconds (2 minutes)
- Confidence Threshold: 60%

## Testing

### Run Integration Tests
```bash
# Test OCR API integration
python test_ocr_integration.py

# Test quality assessment
python test_lambda_ocr_quality.py
```

### Test Coverage
- ✅ Image quality assessment
- ✅ PDF quality assessment (small and large files)
- ✅ Parallel processing for large PDFs
- ✅ Size detection and method selection
- ✅ Confidence metrics calculation
- ✅ API error handling
- ✅ Configuration loading
- ✅ Performance optimization

## Deployment Notes

1. **No Breaking Changes**: Existing code continues to work
2. **Configuration**: Update SSM parameters with OCR service URL
3. **Monitoring**: Monitor OCR API response times and success rates
4. **Fallback**: Keep Textract configuration for emergency rollback

## Files Modified

### Core Files
- `core/services/lambda_ocr_service.py` (NEW)
- `core/services/process.py` (UPDATED)

### Configuration
- `infra/cdk/lib/parameters.ts` (UPDATED)

### Tests
- `test_lambda_ocr_quality.py` (RENAMED & UPDATED)
- `test_ocr_integration.py` (NEW)

### Backup
- `backup_textract/textract_quality.py` (BACKUP)

## Next Steps

1. **Deploy**: Deploy updated Lambda function with new OCR service
2. **Test**: Run integration tests with real documents
3. **Monitor**: Monitor OCR API performance and costs
4. **Optimize**: Adjust timeouts and thresholds based on usage
5. **Cleanup**: Remove old Textract files after successful migration

## Rollback Plan

If issues arise, rollback is simple:
1. Revert imports in `process.py` to use `textract_quality`
2. Restore original `textract_quality.py` from backup
3. Update SSM parameters to use Textract configuration
4. Redeploy Lambda function

The migration maintains full backward compatibility, making rollback straightforward if needed.
