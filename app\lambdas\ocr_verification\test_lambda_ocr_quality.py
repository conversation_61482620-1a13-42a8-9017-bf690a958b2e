#!/usr/bin/env python3
"""
Test Lambda OCR service quality assessment functionality
"""
import os
import sys
import json
import time
from io import BytesIO
from PIL import Image

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.services.lambda_ocr_service import (
    assess_document_quality_with_lambda_ocr,
    assess_pdf_quality_with_lambda_ocr,
    calculate_confidence_metrics
)


def test_confidence_metrics_calculation():
    """Test confidence metrics calculation"""
    print("\n🧪 Testing Confidence Metrics Calculation")
    print("-" * 50)
    
    # Test with sample confidence scores
    test_confidences = [0.95, 0.87, 0.92, 0.78, 0.89, 0.91, 0.85, 0.93]
    
    metrics = calculate_confidence_metrics(test_confidences)
    
    print("📊 Confidence Metrics Results:")
    print(f"   Geometric Mean: {metrics['geometric_mean']:.2f}% (recommended)")
    print(f"   Arithmetic Mean: {metrics['arithmetic_mean']:.2f}%")
    print(f"   Harmonic Mean: {metrics['harmonic_mean']:.2f}%")
    print(f"   Min/Max: {metrics['minimum']:.2f}% / {metrics['maximum']:.2f}%")
    print(f"   Median: {metrics['median']:.2f}%")
    print(f"   Std Deviation: {metrics['std_deviation']:.2f}%")
    print(f"   Total Words: {metrics['total_words']}")
    
    return True


def test_image_quality_assessment(image_path):
    """Test image quality assessment with Lambda OCR service"""
    print(f"\n🧪 Testing Image Quality Assessment: {image_path}")
    print("-" * 50)
    
    try:
        # Load image
        with open(image_path, 'rb') as f:
            image_bytes = f.read()
        
        print(f"📄 Image loaded: {len(image_bytes)} bytes")
        
        # Assess quality
        start_time = time.time()
        result = assess_document_quality_with_lambda_ocr(image_bytes)
        processing_time = time.time() - start_time
        
        print(f"⏱️ Processing time: {processing_time:.2f} seconds")
        
        if result["status"] == "SUCCESS":
            quality_data = result["result"]
            rapid_confidence = quality_data["rapid_confidence"]
            quality_assessment = quality_data["quality_assessment"]
            
            print("✅ Quality Assessment Results:")
            print(f"   Quality Level: {quality_assessment['quality_level']}")
            print(f"   Quality Score: {quality_assessment['quality_score']:.2f}")
            print(f"   Quality Acceptable: {quality_assessment['quality_acceptable']}")
            print(f"   Confidence Threshold: {quality_assessment['confidence_threshold']}%")
            
            print("\n📊 OCR Confidence Metrics:")
            print(f"   Geometric Mean: {rapid_confidence['geometric_mean']:.2f}%")
            print(f"   Total Words: {rapid_confidence['total_words']}")
            
            print(f"\n📝 Assessment Summary:")
            print(f"   {quality_assessment['assessment_summary']}")
            
            # Show sample words with confidence
            words_data = quality_data.get("words_data", [])
            if words_data:
                print(f"\n🔤 Sample Words (first 10):")
                for i, word in enumerate(words_data[:10]):
                    print(f"   {i+1:2d}. '{word['text']}' - {word['confidence']:.1f}%")
            
            return True
        else:
            print(f"❌ Quality assessment failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing image quality assessment: {e}")
        return False


def test_pdf_quality_assessment(pdf_path):
    """Test PDF quality assessment with Lambda OCR service"""
    print(f"\n🧪 Testing PDF Quality Assessment: {pdf_path}")
    print("-" * 50)
    
    try:
        # Load PDF
        with open(pdf_path, 'rb') as f:
            pdf_bytes = f.read()
        
        print(f"📄 PDF loaded: {len(pdf_bytes)} bytes")
        
        # Assess quality
        start_time = time.time()
        result = assess_pdf_quality_with_lambda_ocr(pdf_bytes)
        processing_time = time.time() - start_time
        
        print(f"⏱️ Processing time: {processing_time:.2f} seconds")
        
        if result["status"] == "SUCCESS":
            quality_data = result["result"]
            rapid_confidence = quality_data["rapid_confidence"]
            quality_assessment = quality_data["quality_assessment"]
            page_assessments = quality_data.get("page_assessments", [])
            
            print("✅ PDF Quality Assessment Results:")
            print(f"   Quality Level: {quality_assessment['quality_level']}")
            print(f"   Quality Score: {quality_assessment['quality_score']:.2f}")
            print(f"   Quality Acceptable: {quality_assessment['quality_acceptable']}")
            print(f"   Confidence Threshold: {quality_assessment['confidence_threshold']}%")
            
            print("\n📊 Overall OCR Confidence Metrics:")
            print(f"   Geometric Mean: {rapid_confidence['geometric_mean']:.2f}%")
            print(f"   Total Words: {rapid_confidence['total_words']}")
            
            print(f"\n📄 Page-by-Page Assessment:")
            for page in page_assessments:
                print(f"   Page {page['page']}: {page['confidence']:.1f}% confidence, "
                      f"threshold {page['threshold']}%, accepted: {page['accepted']}")
            
            print(f"\n📝 Assessment Summary:")
            print(f"   {quality_assessment['assessment_summary']}")
            
            return True
        else:
            print(f"❌ PDF quality assessment failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing PDF quality assessment: {e}")
        return False


def create_test_image():
    """Create a simple test image for testing"""
    print("\n🖼️ Creating test image...")
    
    # Create a simple test image with text
    from PIL import Image, ImageDraw, ImageFont
    
    # Create a white image
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # Add some test text
    test_text = [
        "Test Document Quality Assessment",
        "This is a sample document for testing",
        "External OCR API confidence scoring",
        "Multiple lines of text for analysis",
        "Quality metrics calculation test"
    ]
    
    y_position = 50
    for line in test_text:
        draw.text((50, y_position), line, fill='black', font=font)
        y_position += 40
    
    # Save the test image
    test_image_path = "test_document_quality.png"
    img.save(test_image_path)
    print(f"✅ Test image created: {test_image_path}")
    
    return test_image_path


def main():
    """Main test function"""
    print("🔍 External OCR API Quality Assessment Testing")
    print("=" * 50)
    
    # Test confidence metrics calculation
    test_confidence_metrics_calculation()
    
    # Create test image if no test files provided
    test_image_path = create_test_image()
    
    # Test image quality assessment
    test_image_quality_assessment(test_image_path)
    
    # Test PDF quality assessment if sample PDF exists
    sample_pdf_path = "sample_insurance_claim.png"  # Using the existing sample
    if os.path.exists(sample_pdf_path):
        test_pdf_quality_assessment(sample_pdf_path)
    else:
        print(f"\n⚠️ Sample PDF not found: {sample_pdf_path}")
        print("   Skipping PDF quality assessment test")
    
    print("\n✅ Testing completed!")


if __name__ == "__main__":
    main() 