#!/usr/bin/env python3
"""
Test script to verify Gemini function calling implementation
"""
import os
import sys
import json

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.llm.gemini import <PERSON><PERSON><PERSON>
from core.logger.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def test_text_based_extraction():
    """Test text-based extraction with function calling"""
    print("\n🧪 Testing Text-Based Extraction with Function Calling")
    print("-" * 60)
    
    # Sample business rules
    business_rules = ["property_price", "property_size", "contract_value"]
    
    # Create parameter fields
    parameter_fields = ",\n".join([f'      "{rule}": ""' for rule in business_rules])
    
    # Sample text content
    sample_text = """
    Property Details:
    Property Price: 2,500,000 AED
    Property Size: 150 square meters
    Contract Value: 2,500,000 AED
    """
    
    # Create messages in the format expected by the system
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "text": f"""You are an expert in extracting structured information from text. Extract the following parameters:

business rules
- property_price
- property_size  
- contract_value

Format the output as a JSON object with the following structure:
```json
{{ 
  "business_rules": {{
{parameter_fields}
  }}
}}
```

Text to process:
{sample_text}

Return the result as a properly formatted JSON object."""
                }
            ]
        }
    ]
    
    try:
        print("📝 Sending text-based extraction request...")
        response = GeminiLlm.converse_model(messages)
        
        print(f"📊 Response Status: {response.get('status')}")
        print(f"📊 Response Message: {response.get('message', 'N/A')}")
        
        if response.get('status') == 'SUCCESS':
            result = response.get('result', '')
            print(f"📄 Raw Result Preview: {result[:200]}...")
            
            # Try to parse the result
            try:
                # Extract JSON from <json> tags if present
                if '<json>' in result and '</json>' in result:
                    start = result.find('<json>') + 6
                    end = result.find('</json>')
                    json_str = result[start:end]
                else:
                    json_str = result
                
                parsed = json.loads(json_str)
                print("✅ Successfully parsed JSON response!")
                print(f"📋 Business Rules: {parsed.get('business_rules', {})}")
                print(f"📋 LLM Quality: {parsed.get('llm_quality', {})}")
                
                return True
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON: {e}")
                return False
        else:
            print(f"❌ Request failed: {response.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

def test_image_based_extraction():
    """Test image-based extraction with function calling"""
    print("\n🧪 Testing Image-Based Extraction with Function Calling")
    print("-" * 60)
    
    # Sample business rules
    business_rules = ["property_price", "property_size", "contract_value"]
    
    # Create parameter fields
    parameter_fields = ",\n".join([f'      "{rule}": ""' for rule in business_rules])
    
    # Create a simple test image (1x1 pixel) for testing
    from PIL import Image
    import io
    
    # Create a minimal test image
    test_image = Image.new('RGB', (1, 1), color='white')
    img_byte_arr = io.BytesIO()
    test_image.save(img_byte_arr, format='JPEG')
    img_byte_arr = img_byte_arr.getvalue()
    
    # Create messages in the format expected by the system
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "text": f"""You are an expert in extracting structured information from the provided input. Extract the ACTUAL VALUES from the document for ONLY these parameters:

## Input:
1. property_price
2. property_size
3. contract_value

Format the output as a JSON object with the following structure:
```json
{{ 
  "business_rules": {{
{parameter_fields}
  }},
  "llm_quality": {{
    "text_clarity": "[0.0-1.0 score for text sharpness, text recognition, clarity and overall readability of ANY text in the image]",
    "contrast_readability": "[0.0-1.0 score for text-background contrast]",
    "resolution_text_size": "[0.0-1.0 score for resolution and text size adequacy]",
    "overall_image_quality": "[0.0-1.0 score for overall brightness, exposure, and noise levels]",
    "assessment_summary": "Detailed technical summary explicitly mentioning specific quality issues observed"
  }}
}}
```

Return the result as a properly formatted JSON object."""
                },
                {
                    "image": {
                        "format": "jpeg",
                        "source": {
                            "bytes": img_byte_arr
                        }
                    }
                }
            ]
        }
    ]
    
    try:
        print("📝 Sending image-based extraction request...")
        response = GeminiLlm.converse_model(messages)
        
        print(f"📊 Response Status: {response.get('status')}")
        print(f"📊 Response Message: {response.get('message', 'N/A')}")
        
        if response.get('status') == 'SUCCESS':
            result = response.get('result', '')
            print(f"📄 Raw Result Preview: {result[:200]}...")
            
            # Try to parse the result
            try:
                # Extract JSON from <json> tags if present
                if '<json>' in result and '</json>' in result:
                    start = result.find('<json>') + 6
                    end = result.find('</json>')
                    json_str = result[start:end]
                else:
                    json_str = result
                
                parsed = json.loads(json_str)
                print("✅ Successfully parsed JSON response!")
                print(f"📋 Business Rules: {parsed.get('business_rules', {})}")
                print(f"📋 LLM Quality: {parsed.get('llm_quality', {})}")
                
                return True
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON: {e}")
                return False
        else:
            print(f"❌ Request failed: {response.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Gemini Function Calling Implementation")
    print("=" * 60)
    
    # Check if Gemini API key is available
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        print("❌ GEMINI_API_KEY environment variable not set")
        print("   Please set GEMINI_API_KEY to run these tests")
        return False
    
    print(f"✅ Gemini API key found: {gemini_api_key[:10]}...")
    
    # Run tests
    text_success = test_text_based_extraction()
    image_success = test_image_based_extraction()
    
    print("\n📊 Test Results Summary")
    print("-" * 30)
    print(f"Text-based extraction: {'✅ PASS' if text_success else '❌ FAIL'}")
    print(f"Image-based extraction: {'✅ PASS' if image_success else '❌ FAIL'}")
    
    overall_success = text_success and image_success
    print(f"\nOverall result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
