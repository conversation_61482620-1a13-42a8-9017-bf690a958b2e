#!/usr/bin/env python3
"""
Test script to verify the optimized Gemini implementation with function calling
"""
import os
import sys
import json

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.llm.gemini import GeminiLlm
from core.services.process import create_extraction_prompt, extract_parameters_from_image
from core.logger.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def test_optimized_text_extraction():
    """Test the optimized text-based extraction"""
    print("\n🧪 Testing Optimized Text-Based Extraction")
    print("-" * 60)
    
    # Sample business rules and text
    business_rules = ["property_price", "property_size", "contract_value"]
    sample_text = """
    Property Details:
    Property Price: 2,500,000 AED
    Property Size: 150 square meters
    Contract Value: 2,500,000 AED
    Location: Dubai Marina
    """
    
    try:
        # Create prompt using the optimized function
        prompt = create_extraction_prompt(sample_text, business_rules)
        print(f"📝 Generated prompt preview: {prompt[:300]}...")
        
        # Create messages for Gemini
        messages = [
            {
                "role": "user",
                "content": [{"text": prompt}]
            }
        ]
        
        # Call Gemini with enforced function calling
        print("📝 Sending request to Gemini with ENFORCED function calling...")
        print(f"📋 Business rules for dynamic schema: {business_rules}")
        response = GeminiLlm.converse_model(messages, business_rules=business_rules)
        
        print(f"📊 Response Status: {response.get('status')}")
        
        if response.get('status') == 'SUCCESS':
            result = response.get('result', '')
            print(f"📄 Raw Result: {result}")
            
            # Try to parse the result
            try:
                # Extract JSON from <json> tags if present
                if '<json>' in result and '</json>' in result:
                    start = result.find('<json>') + 6
                    end = result.find('</json>')
                    json_str = result[start:end]
                else:
                    json_str = result
                
                parsed = json.loads(json_str)
                print("✅ Successfully parsed JSON response!")
                print(f"📋 Business Rules: {json.dumps(parsed.get('business_rules', {}), indent=2)}")
                
                return True
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON: {e}")
                return False
        else:
            print(f"❌ Request failed: {response.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

def test_optimized_image_extraction():
    """Test the optimized image-based extraction"""
    print("\n🧪 Testing Optimized Image-Based Extraction")
    print("-" * 60)
    
    # Sample business rules
    business_rules = ["property_price", "property_size", "contract_value"]
    
    try:
        # Create a simple test image
        from PIL import Image
        import io
        
        # Create a minimal test image with some text-like patterns
        test_image = Image.new('RGB', (800, 600), color='white')
        
        # Test the extract_parameters_from_image function
        print("📝 Testing image extraction with optimized prompt...")
        response = extract_parameters_from_image(test_image, business_rules)
        
        print(f"📊 Response Status: {response.get('status')}")
        
        if response.get('status') == 'SUCCESS':
            result = response.get('result', {})
            print(f"📄 Extraction Result: {json.dumps(result, indent=2)}")
            
            # Check if we have the expected structure
            if 'business_rules' in result and 'llm_quality' in result:
                print("✅ Response has correct structure (business_rules + llm_quality)")
                return True
            elif 'business_rules' in result:
                print("✅ Response has business_rules (quality might be added by normalization)")
                return True
            else:
                print("❌ Response missing expected structure")
                return False
        else:
            print(f"❌ Request failed: {response.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

def test_function_calling_detection():
    """Test that function calling is properly detected and configured"""
    print("\n🧪 Testing Function Calling Detection")
    print("-" * 60)
    
    try:
        # Test with text content (should use extract_text_data function)
        text_messages = [
            {
                "role": "user",
                "content": [{"text": "Extract data from this text: Property Price: 1000000"}]
            }
        ]
        
        print("📝 Testing text-based function calling setup...")
        response = GeminiLlm.converse_model(text_messages)
        print(f"📊 Text-based response status: {response.get('status')}")
        
        # Test with image content (should use extract_document_data function)
        from PIL import Image
        import io
        
        test_image = Image.new('RGB', (100, 100), color='white')
        img_byte_arr = io.BytesIO()
        test_image.save(img_byte_arr, format='JPEG')
        img_bytes = img_byte_arr.getvalue()
        
        image_messages = [
            {
                "role": "user",
                "content": [
                    {"text": "Extract data from this image"},
                    {"image": {"format": "jpeg", "source": {"bytes": img_bytes}}}
                ]
            }
        ]
        
        print("📝 Testing image-based function calling setup...")
        response = GeminiLlm.converse_model(image_messages)
        print(f"📊 Image-based response status: {response.get('status')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during function calling test: {e}")
        return False

def main():
    """Run all optimization tests"""
    print("🚀 Testing Gemini Optimizations")
    print("=" * 60)
    
    # Check if Gemini API key is available
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if not gemini_api_key:
        print("❌ GEMINI_API_KEY environment variable not set")
        print("   Please set GEMINI_API_KEY to run these tests")
        return False
    
    print(f"✅ Gemini API key found: {gemini_api_key[:10]}...")
    
    # Run tests
    text_success = test_optimized_text_extraction()
    image_success = test_optimized_image_extraction()
    function_success = test_function_calling_detection()
    
    print("\n📊 Test Results Summary")
    print("-" * 30)
    print(f"Text extraction: {'✅ PASS' if text_success else '❌ FAIL'}")
    print(f"Image extraction: {'✅ PASS' if image_success else '❌ FAIL'}")
    print(f"Function calling: {'✅ PASS' if function_success else '❌ FAIL'}")
    
    overall_success = text_success and image_success and function_success
    print(f"\nOverall result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
